package com.dbauth.gateway.api;

import com.dbauth.gateway.service.JwtRestTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * User Permission Management Proxy Controller
 * Routes requests to user-management service
 */
@RestController
@RequestMapping("/api/user-permissions")
@Tag(name = "User Permission Management", description = "User permission management operations")
public class UserPermissionController {

    @Autowired
    private JwtRestTemplateService restTemplateService;

    @Value("${gateway.user-management-url}")
    private String userManagementUrl;

    @GetMapping
    @Operation(summary = "Get all user permissions", description = "Retrieve all user permissions")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> getAllUserPermissions() {
        String url = userManagementUrl + "/api/user-permissions";
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.GET, null);
    }

    @PostMapping
    @Operation(summary = "Create user permission", description = "Grant permission to a user")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> createUserPermission(@RequestBody Map<String, Object> permissionData) {
        String url = userManagementUrl + "/api/user-permissions";
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.POST, permissionData);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get user permission by ID", description = "Retrieve a specific user permission by ID")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> getUserPermissionById(@PathVariable String id) {
        String url = userManagementUrl + "/api/user-permissions/" + id;
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.GET, null);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete user permission", description = "Revoke a user permission")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> deleteUserPermission(@PathVariable String id) {
        String url = userManagementUrl + "/api/user-permissions/" + id;
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.DELETE, null);
    }

    @GetMapping("/database-roles/{serverId}")
    @Operation(summary = "Get database roles", description = "Get available database roles for a server")
    public ResponseEntity<?> getDatabaseRoles(@PathVariable String serverId) {
        String url = userManagementUrl + "/api/user-permissions/database-roles/" + serverId;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/user/{userId}")
    @Operation(summary = "Get permissions by user", description = "Get all permissions for a specific user")
    public ResponseEntity<?> getPermissionsByUser(@PathVariable String userId) {
        String url = userManagementUrl + "/api/user-permissions/user/" + userId;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/server/{serverId}")
    @Operation(summary = "Get permissions by server", description = "Get all permissions for a specific server")
    public ResponseEntity<?> getPermissionsByServer(@PathVariable String serverId) {
        String url = userManagementUrl + "/api/user-permissions/server/" + serverId;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @PostMapping("/{id}/apply")
    @Operation(summary = "Apply user permissions", description = "Apply permissions to the database")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> applyUserPermissions(@PathVariable String id) {
        String url = userManagementUrl + "/api/user-permissions/" + id + "/apply";
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.POST, null);
    }

    @PostMapping("/{id}/revoke")
    @Operation(summary = "Revoke user permissions", description = "Revoke permissions from the database")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> revokeUserPermissions(@PathVariable String id) {
        String url = userManagementUrl + "/api/user-permissions/" + id + "/revoke";
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.POST, null);
    }

    @PostMapping("/user/{userId}/server/{serverId}/apply")
    @Operation(summary = "Apply all user permissions for server", description = "Apply all permissions for a user on a specific server")
    public ResponseEntity<?> applyUserServerPermissions(@PathVariable String userId, @PathVariable String serverId) {
        String url = userManagementUrl + "/api/user-permissions/user/" + userId + "/server/" + serverId + "/apply";
        return restTemplateService.exchange(url, HttpMethod.POST, null, Object.class);
    }

    @PostMapping("/user/{userId}/server/{serverId}/revoke")
    @Operation(summary = "Revoke all user permissions for server", description = "Revoke all permissions for a user on a specific server")
    public ResponseEntity<?> revokeUserServerPermissions(@PathVariable String userId, @PathVariable String serverId) {
        String url = userManagementUrl + "/api/user-permissions/user/" + userId + "/server/" + serverId + "/revoke";
        return restTemplateService.exchange(url, HttpMethod.POST, null, Object.class);
    }

    @GetMapping("/history")
    @Operation(summary = "Get permission history", description = "Retrieve permission history")
    public ResponseEntity<?> getPermissionHistory() {
        String url = userManagementUrl + "/api/user-permissions/history";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/history/grouped")
    @Operation(summary = "Get grouped permission history", description = "Retrieve grouped permission history")
    public ResponseEntity<?> getGroupedPermissionHistory() {
        String url = userManagementUrl + "/api/user-permissions/history/grouped";
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/history/user/{userId}/server/{serverId}")
    @Operation(summary = "Get permission history by user and server", description = "Retrieve permission history for specific user and server")
    public ResponseEntity<?> getPermissionHistoryByUserAndServer(@PathVariable String userId, @PathVariable String serverId) {
        String url = userManagementUrl + "/api/user-permissions/history/user/" + userId + "/server/" + serverId;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }

    @GetMapping("/history/detailed/user/{userId}/server/{serverId}")
    @Operation(summary = "Get detailed permission history by user and server", description = "Retrieve detailed permission history for specific user and server (each action as separate record)")
    public ResponseEntity<?> getDetailedPermissionHistoryByUserAndServer(@PathVariable String userId, @PathVariable String serverId) {
        String url = userManagementUrl + "/api/user-permissions/history/detailed/user/" + userId + "/server/" + serverId;
        return restTemplateService.exchange(url, HttpMethod.GET, null, Object.class);
    }
}
