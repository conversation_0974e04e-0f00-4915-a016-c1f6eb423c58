package com.dbauth.gateway.api;

import com.dbauth.gateway.service.JwtRestTemplateService;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.tags.Tag;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.Map;

/**
 * User Management Proxy Controller
 * Routes requests to user-management service
 */
@RestController
@RequestMapping("/api/users")
@Tag(name = "User Management", description = "User management operations")
public class UserController {

    @Autowired
    private JwtRestTemplateService restTemplateService;

    @Value("${gateway.user-management-url}")
    private String userManagementUrl;

    @GetMapping
    @Operation(summary = "Get all users", description = "Retrieve all users")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> getAllUsers() {
        String url = userManagementUrl + "/api/users";
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.GET, null);
    }

    @PostMapping
    @Operation(summary = "Create user", description = "Create a new user")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> createUser(@RequestBody Map<String, Object> userData) {
        String url = userManagementUrl + "/api/users";
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.POST, userData);
    }

    @GetMapping("/{id}")
    @Operation(summary = "Get user by ID", description = "Retrieve a specific user by ID")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> getUserById(@PathVariable String id) {
        String url = userManagementUrl + "/api/users/" + id;
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.GET, null);
    }

    @PutMapping("/{id}")
    @Operation(summary = "Update user", description = "Update an existing user")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> updateUser(@PathVariable String id, @RequestBody Map<String, Object> userData) {
        String url = userManagementUrl + "/api/users/" + id;
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.PUT, userData);
    }

    @DeleteMapping("/{id}")
    @Operation(summary = "Delete user", description = "Delete a user")
    public ResponseEntity<com.dbauth.shared.dto.ApiResponse<Object>> deleteUser(@PathVariable String id) {
        String url = userManagementUrl + "/api/users/" + id;
        return restTemplateService.exchangeWithWrapper(url, HttpMethod.DELETE, null);
    }
}
